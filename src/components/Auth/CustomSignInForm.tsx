import React, { useState } from 'react';
import { useClerk } from '@clerk/clerk-react';
import { Link, useNavigate } from 'react-router-dom';

interface CustomSignInFormProps {
  redirectUrl?: string;
  onSuccess?: () => void;
}

export const CustomSignInForm: React.FC<CustomSignInFormProps> = ({
  redirectUrl = '/',
  onSuccess,
}) => {
  const clerk = useClerk();
  const navigate = useNavigate();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [resetCode, setResetCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [resetStep, setResetStep] = useState<'email' | 'code' | 'password'>('email');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      setError('Vennligst fyll ut alle feltene');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create sign-in attempt
      const signInAttempt = await clerk.client?.signIn.create({
        identifier: email,
        password,
      });

      if (signInAttempt?.status === 'complete') {
        // Sign-in successful
        await clerk.setActive({ session: signInAttempt.createdSessionId });

        if (onSuccess) {
          onSuccess();
        } else {
          navigate(redirectUrl);
        }
      } else {
        // Handle additional verification steps if needed
        setError('Innlogging krever ytterligere verifisering');
      }
    } catch (err: any) {
      console.error('Sign-in error:', err);

      // Handle specific error cases
      const errorCode = err.errors?.[0]?.code || err.code;
      const errorMessage = err.errors?.[0]?.message || err.message || '';

      if (errorCode === 'form_identifier_not_found' || errorMessage.includes('not found')) {
        setError('Ingen konto funnet med denne e-postadressen');
      } else if (errorCode === 'form_password_incorrect' || errorMessage.includes('password')) {
        setError('Feil passord. Prøv igjen');
      } else if (errorCode === 'too_many_requests' || errorMessage.includes('rate limit')) {
        setError('For mange forsøk. Vent litt før du prøver igjen');
      } else if (errorMessage.includes('blocked') || errorMessage.includes('suspended')) {
        setError('Kontoen din er midlertidig blokkert. Kontakt support');
      } else {
        setError('Innlogging feilet. Sjekk e-post og passord');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetEmail.trim()) {
      setError('Vennligst oppgi e-postadressen din');
      return;
    }

    setIsResettingPassword(true);
    setError(null);

    try {
      // Create password reset attempt
      const signInAttempt = await clerk.client?.signIn.create({
        identifier: resetEmail,
        strategy: 'reset_password_email_code',
      });

      if (signInAttempt?.status === 'needs_first_factor') {
        await signInAttempt.prepareFirstFactor({
          strategy: 'reset_password_email_code',
          emailAddressId: signInAttempt.supportedFirstFactors[0].emailAddressId,
        });
        setResetStep('code');
      }
    } catch (err: any) {
      console.error('Password reset error:', err);

      const errorCode = err.errors?.[0]?.code || err.code;
      const errorMessage = err.errors?.[0]?.message || err.message || '';

      if (errorCode === 'form_identifier_not_found' || errorMessage.includes('not found')) {
        setError('Ingen konto funnet med denne e-postadressen');
      } else {
        setError('Kunne ikke sende tilbakestillingslenke. Prøv igjen senere');
      }
    } finally {
      setIsResettingPassword(false);
    }
  };

  const handleResetCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetCode.trim()) {
      setError('Vennligst oppgi verifiseringskoden');
      return;
    }

    setIsResettingPassword(true);
    setError(null);

    try {
      const signInAttempt = await clerk.client?.signIn.attemptFirstFactor({
        strategy: 'reset_password_email_code',
        code: resetCode,
      });

      if (signInAttempt?.status === 'needs_new_password') {
        setResetStep('password');
      }
    } catch (err: any) {
      console.error('Reset code verification error:', err);
      setError('Ugyldig verifiseringskode. Prøv igjen');
    } finally {
      setIsResettingPassword(false);
    }
  };

  const handleNewPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPassword.trim()) {
      setError('Vennligst oppgi nytt passord');
      return;
    }

    if (newPassword.length < 8) {
      setError('Passordet må være minst 8 tegn langt');
      return;
    }

    setIsResettingPassword(true);
    setError(null);

    try {
      const signInAttempt = await clerk.client?.signIn.resetPassword({
        password: newPassword,
      });

      if (signInAttempt?.status === 'complete') {
        await clerk.setActive({ session: signInAttempt.createdSessionId });

        if (onSuccess) {
          onSuccess();
        } else {
          navigate(redirectUrl);
        }
      }
    } catch (err: any) {
      console.error('New password error:', err);
      setError('Kunne ikke oppdatere passord. Prøv igjen');
    } finally {
      setIsResettingPassword(false);
    }
  };

  // Password reset flow
  if (showPasswordReset) {
    if (resetStep === 'email') {
      return (
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m-2 2V3a2 2 0 00-2-2m2 2a2 2 0 012 2z" />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
              Tilbakestill passord
            </h1>
            <p className="text-sm sm:text-base text-jobblogg-text-medium">
              Oppgi e-postadressen din så sender vi deg en tilbakestillingslenke
            </p>
          </div>

          {/* Reset Form */}
          <form onSubmit={handlePasswordReset} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="resetEmail" className="block text-sm font-medium text-jobblogg-text-strong">
                E-postadresse
              </label>
              <input
                type="email"
                id="resetEmail"
                value={resetEmail}
                onChange={(e) => setResetEmail(e.target.value)}
                className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
                placeholder="<EMAIL>"
                autoComplete="email"
                required
                disabled={isResettingPassword}
              />
            </div>

            {error && (
              <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isResettingPassword}
              className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
            >
              {isResettingPassword ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Sender...</span>
                </>
              ) : (
                <span>Send tilbakestillingslenke</span>
              )}
            </button>
          </form>

          {/* Back to sign in */}
          <div className="text-center">
            <button
              onClick={() => {
                setShowPasswordReset(false);
                setError(null);
                setResetEmail('');
                setResetStep('email');
              }}
              className="text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] px-4 py-2"
            >
              ← Tilbake til innlogging
            </button>
          </div>
        </div>
      );
    }

    // Code verification step
    if (resetStep === 'code') {
      return (
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
              Sjekk e-posten din
            </h1>
            <p className="text-sm sm:text-base text-jobblogg-text-medium">
              Vi har sendt en verifiseringskode til <strong>{resetEmail}</strong>
            </p>
          </div>

          {/* Code Form */}
          <form onSubmit={handleResetCodeSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="resetCode" className="block text-sm font-medium text-jobblogg-text-strong">
                Verifiseringskode
              </label>
              <input
                type="text"
                id="resetCode"
                value={resetCode}
                onChange={(e) => setResetCode(e.target.value)}
                className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted text-center text-lg tracking-widest min-h-[44px]"
                placeholder="123456"
                maxLength={6}
                autoComplete="one-time-code"
                required
                disabled={isResettingPassword}
              />
            </div>

            {error && (
              <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isResettingPassword}
              className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
            >
              {isResettingPassword ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Verifiserer...</span>
                </>
              ) : (
                <span>Bekreft kode</span>
              )}
            </button>
          </form>

          {/* Back option */}
          <div className="text-center">
            <button
              onClick={() => setResetStep('email')}
              className="text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] px-4 py-2"
            >
              ← Tilbake
            </button>
          </div>
        </div>
      );
    }

    // New password step
    if (resetStep === 'password') {
      return (
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m-2 2V3a2 2 0 00-2-2m2 2a2 2 0 012 2z" />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
              Opprett nytt passord
            </h1>
            <p className="text-sm sm:text-base text-jobblogg-text-medium">
              Velg et sterkt passord for kontoen din
            </p>
          </div>

          {/* New Password Form */}
          <form onSubmit={handleNewPasswordSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="newPassword" className="block text-sm font-medium text-jobblogg-text-strong">
                Nytt passord
              </label>
              <input
                type="password"
                id="newPassword"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
                placeholder="Minst 8 tegn"
                autoComplete="new-password"
                minLength={8}
                required
                disabled={isResettingPassword}
              />
            </div>

            {error && (
              <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isResettingPassword}
              className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
            >
              {isResettingPassword ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Oppdaterer...</span>
                </>
              ) : (
                <span>Oppdater passord</span>
              )}
            </button>
          </form>
        </div>
      );
    }
  }

  // Main sign-in form
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
          Logg inn på JobbLogg
        </h1>
        <p className="text-sm sm:text-base text-jobblogg-text-medium">
          Fortsett med prosjektdokumentasjonen din
        </p>
      </div>

      {/* Sign-in Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
            E-postadresse
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
            placeholder="<EMAIL>"
            autoComplete="email"
            required
            disabled={isSubmitting}
          />
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="password" className="block text-sm font-medium text-jobblogg-text-strong">
              Passord
            </label>
            <button
              type="button"
              onClick={() => {
                setShowPasswordReset(true);
                setResetEmail(email);
                setError(null);
              }}
              className="text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] px-2 py-1"
            >
              Glemt passord?
            </button>
          </div>
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 pr-12 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
              placeholder="Skriv inn passordet ditt"
              autoComplete="current-password"
              required
              disabled={isSubmitting}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted hover:text-jobblogg-text-strong transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
              disabled={isSubmitting}
              aria-label={showPassword ? 'Skjul passord' : 'Vis passord'}
            >
              {showPassword ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Logger inn...</span>
            </>
          ) : (
            <span>Logg inn</span>
          )}
        </button>
      </form>

      {/* Footer Links */}
      <div className="text-center space-y-4">
        <div className="text-sm sm:text-base text-jobblogg-text-medium">
          Har du ikke konto?{' '}
          <Link
            to="/sign-up"
            className="font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] inline-flex items-center px-2 py-1"
          >
            Registrer deg her
          </Link>
        </div>

        {/* Trust indicator */}
        <div className="flex items-center justify-center space-x-2 text-xs text-jobblogg-text-muted">
          <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <span>Sikret av Clerk</span>
        </div>
      </div>
    </div>
  );
};
